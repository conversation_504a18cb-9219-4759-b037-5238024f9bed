<?php
header('Content-Type: application/json; charset=utf-8');
include('webset.php');
include('session.php');
//-------------------------------------------------------------------------------
//-------------------------------------------------------------------------------
if(isset($_POST['do']) && $_POST['do'] == 'ConfirmBookTrip'){
    if($_POST['bt_name'] == ''){
        echo Show_Alert('danger' , 'من فضلك أدخل الأسم بالكامل.');
    }else if($_POST['bt_phone'] == ''){
        echo Show_Alert('danger' , 'من فضلك أدخل رقم الجوال.');
    }elseif(strlen($_POST['bt_phone']) < 8){
        echo Show_Alert('danger' , 'من فضلك أدخل رقم جوال صحيح.');
    }else{

            $stmt = $db->prepare("INSERT INTO orders ( name , phone , hamla_title , hamla_id , offer_title , offer_id , people , datee , madina , payment , room_title , return_date , total_price , discount ) VALUES (:user_1 ,:user_2 ,:user_3 ,:user_4 ,:user_5 ,:user_6 ,:user_7 ,:user_8 ,:user_9 ,:user_10 ,:user_11 ,:user_12 ,:user_13 ,:user_14 )");  
            $stmt->execute(array(
                'user_1' => $_POST['bt_name'] ,
                'user_2' => $_POST['bt_phone'] ,
                'user_3' => $_POST['hamla'] ,
                'user_4' => $_POST['id'] ,
                'user_5' => $_POST['title'] ,
                'user_6' => $_POST['bt_book'] ,
                'user_7' => $_POST['bt_people'] ,
                'user_8' => $_POST['date_timepicker_end'] ,
                'user_9' => $_POST['bt_mvist'] ,
                'user_10' => $_POST['selectedPayment'] ,
                'user_11' => $_POST['room_title'] ,
                'user_12' => $_POST['return_date_display'] ,
                'user_13' => $_POST['total_price'] ,
                'user_14' => $_POST['discount_display'] 
            ));
            echo 'done'; 

            $_SESSION['Omra'] = [
                'bt_name' => $_POST['bt_name'] ,
                'bt_phone' => $_POST['bt_phone'] ,
                'hamla' => $_POST['hamla'] , 
                'id' => $_POST['id'] , 
                'title' => $_POST['title'] , 
                'bt_book' => $_POST['bt_book'] , 
                'bt_people' => $_POST['bt_people'] , 
                'date_timepicker_end' => $_POST['date_timepicker_end'] , 
                'bt_mvist' => $_POST['bt_mvist'] , 
                'selectedPayment' => $_POST['selectedPayment'] , 
                'room_title' => $_POST['room_title'] , 
                
                'return_date_display' => $_POST['return_date_display'] , 
                'total_price' => $_POST['total_price'] , 
                'discount_display' => $_POST['discount_display'] , 
            ];


            $name  = $_POST['bt_name']  ?? '---';
            $phone =  $_POST['bt_phone'] ?? '---';
            $trip  = $_POST['title']  ?? '---';

            // الرسالة اللي هتتبعت
            $message = "تم تأكيد الحجز بنجاح:\n";
            $message .= "الاسم: $name\n";
            $message .= "الجوال: $phone\n";
            $message .= "الرحلة: ".$_POST['hamla'].PHP_EOL;
            $message .= "الغرفه: ".$_POST['room_title'].PHP_EOL;
            $message .= "تاريخ الرحله: ".$_POST['date_timepicker_end'].PHP_EOL;
            $message .= "التكلفه: ".$_POST['total_price'].PHP_EOL;



            $telegram_token = "**********************************************";
            $telegram_channel = "-4800733652";
        
            $data = [
                'chat_id' => $telegram_channel,
                'text' => $message 
            ];
            // 'teeeee' . PHP_EOL . PHP_EOL . 'test',
            try {
            $ch = curl_init("https://api.telegram.org/bot$telegram_token/sendMessage");
            curl_setopt($ch, CURLOPT_HEADER, false);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            $result = curl_exec($ch);
            curl_close($ch);
            } catch (Exception $e) {} 

    }
//-------------------------------------------------------------------------------

// وظائف نظام حجز الباصات
if (isset($_POST['action'])) {
    $action = $_POST['action'];

    try {
        switch ($action) {
            case 'get_stations':
                if (!isset($_POST['city_id']) || empty($_POST['city_id'])) {
                    echo json_encode(['success' => false, 'message' => 'لم يتم تحديد المدينة']);
                    exit;
                }

                $city_id = intval($_POST['city_id']);
                $stmt = $db->prepare("SELECT * FROM stations WHERE city_id = ? AND is_active = 1 ORDER BY name");
                $stmt->execute([$city_id]);
                $stations = $stmt->fetchAll(PDO::FETCH_ASSOC);

                echo json_encode(['success' => true, 'stations' => $stations]);
                break;

            case 'search_trips':
                $trip_type = $_POST['trip_type'] ?? '';
                $station_id = intval($_POST['station_id'] ?? 0);
                $trip_date = $_POST['trip_date'] ?? '';
                $passengers = intval($_POST['passengers'] ?? 0);

                if (empty($trip_type) || $station_id == 0 || empty($trip_date) || $passengers == 0) {
                    echo json_encode(['success' => false, 'message' => 'يرجى ملء جميع الحقول المطلوبة']);
                    exit;
                }

                // التحقق من صحة التاريخ
                $selected_date = new DateTime($trip_date);
                $today = new DateTime();
                if ($selected_date < $today) {
                    echo json_encode(['success' => false, 'message' => 'لا يمكن حجز رحلة في تاريخ سابق']);
                    exit;
                }

                // الحصول على يوم الأسبوع (0 = الأحد، 1 = الاثنين، إلخ)
                $day_of_week = $selected_date->format('w');

                // البحث عن الرحلات
                if ($trip_type == 'to_makkah') {
                    // رحلات إلى مكة
                    $sql = "SELECT t.*,
                            ds.name as departure_station,
                            as_table.name as arrival_station,
                            dc.name as departure_city,
                            ac.name as arrival_city
                            FROM trips t
                            JOIN stations ds ON t.departure_station_id = ds.id
                            JOIN stations as_table ON t.arrival_station_id = as_table.id
                            JOIN cities dc ON ds.city_id = dc.id
                            JOIN cities ac ON as_table.city_id = ac.id
                            WHERE t.trip_type = 'to_makkah'
                            AND t.departure_station_id = ?
                            AND t.is_active = 1
                            AND (t.available_days = '0' OR JSON_CONTAINS(t.available_days, ?))
                            ORDER BY t.departure_time";
                } else {
                    // رحلات من مكة
                    $sql = "SELECT t.*,
                            ds.name as departure_station,
                            as_table.name as arrival_station,
                            dc.name as departure_city,
                            ac.name as arrival_city
                            FROM trips t
                            JOIN stations ds ON t.departure_station_id = ds.id
                            JOIN stations as_table ON t.arrival_station_id = as_table.id
                            JOIN cities dc ON ds.city_id = dc.id
                            JOIN cities ac ON as_table.city_id = ac.id
                            WHERE t.trip_type = 'from_makkah'
                            AND t.arrival_station_id = ?
                            AND t.is_active = 1
                            AND (t.available_days = '0' OR JSON_CONTAINS(t.available_days, ?))
                            ORDER BY t.departure_time";
                }

                $stmt = $db->prepare($sql);
                $stmt->execute([$station_id, '"' . $day_of_week . '"']);
                $trips = $stmt->fetchAll(PDO::FETCH_ASSOC);

                if (empty($trips)) {
                    echo json_encode(['success' => false, 'message' => 'لا توجد رحلات متاحة في هذا التاريخ']);
                    exit;
                }

                echo json_encode(['success' => true, 'trips' => $trips]);
                break;

            case 'create_booking':
                $trip_id = intval($_POST['trip_id'] ?? 0);
                $trip_date = $_POST['trip_date'] ?? '';
                $customer_name = trim($_POST['customer_name'] ?? '');
                $customer_phone = trim($_POST['customer_phone'] ?? '');
                $passengers = intval($_POST['passengers'] ?? 0);

                if ($trip_id == 0 || empty($trip_date) || empty($customer_name) || empty($customer_phone) || $passengers == 0) {
                    echo json_encode(['success' => false, 'message' => 'يرجى ملء جميع الحقول المطلوبة']);
                    exit;
                }

                // التحقق من وجود الرحلة
                $stmt = $db->prepare("SELECT * FROM trips WHERE id = ? AND is_active = 1");
                $stmt->execute([$trip_id]);
                $trip = $stmt->fetch(PDO::FETCH_ASSOC);

                if (!$trip) {
                    echo json_encode(['success' => false, 'message' => 'الرحلة غير موجودة']);
                    exit;
                }

                // حساب المبلغ الإجمالي
                $total_price = $trip['seat_price'] * $passengers;

                // إنشاء رقم حجز فريد
                $booking_reference = 'BUS' . date('Ymd') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);

                // التحقق من عدم تكرار رقم الحجز
                $stmt = $db->prepare("SELECT COUNT(*) FROM bus_bookings WHERE booking_reference = ?");
                $stmt->execute([$booking_reference]);
                while ($stmt->fetchColumn() > 0) {
                    $booking_reference = 'BUS' . date('Ymd') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
                    $stmt->execute([$booking_reference]);
                }

                // إدراج الحجز
                $stmt = $db->prepare("INSERT INTO bus_bookings (trip_id, trip_date, customer_name, customer_phone, seats_requested, total_price, booking_reference) VALUES (?, ?, ?, ?, ?, ?, ?)");
                $result = $stmt->execute([$trip_id, $trip_date, $customer_name, $customer_phone, $passengers, $total_price, $booking_reference]);

                if ($result) {
                    echo json_encode([
                        'success' => true,
                        'message' => 'تم الحجز بنجاح',
                        'booking_reference' => $booking_reference
                    ]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء حفظ الحجز']);
                }
                break;

            default:
                // لا نفعل شيء للعمليات الأخرى
                break;
        }

    } catch (Exception $e) {
        error_log("Bus booking error: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'حدث خطأ في النظام']);
    }
}
//-------------------------------------------------------------------------------

}