<?php
header('Content-Type: application/json; charset=utf-8');
include('config.php');

if (!isset($_POST['action'])) {
    echo json_encode(['success' => false, 'message' => 'لم يتم تحديد العملية المطلوبة']);
    exit;
}

$action = $_POST['action'];

try {
    switch ($action) {
        case 'get_stations':
            if (!isset($_POST['city_id']) || empty($_POST['city_id'])) {
                echo json_encode(['success' => false, 'message' => 'لم يتم تحديد المدينة']);
                exit;
            }
            
            $city_id = intval($_POST['city_id']);
            $stmt = $db->prepare("SELECT * FROM stations WHERE city_id = ? AND is_active = 1 ORDER BY name");
            $stmt->execute([$city_id]);
            $stations = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode(['success' => true, 'stations' => $stations]);
            break;
            
        case 'search_trips':
            $trip_type = $_POST['trip_type'] ?? '';
            $station_id = intval($_POST['station_id'] ?? 0);
            $trip_date = $_POST['trip_date'] ?? '';
            $passengers = intval($_POST['passengers'] ?? 0);
            
            if (empty($trip_type) || $station_id == 0 || empty($trip_date) || $passengers == 0) {
                echo json_encode(['success' => false, 'message' => 'يرجى ملء جميع الحقول المطلوبة']);
                exit;
            }
            
            // التحقق من صحة التاريخ
            $selected_date = new DateTime($trip_date);
            $today = new DateTime();
            if ($selected_date < $today) {
                echo json_encode(['success' => false, 'message' => 'لا يمكن حجز رحلة في تاريخ سابق']);
                exit;
            }
            
            // الحصول على يوم الأسبوع (0 = الأحد، 1 = الاثنين، إلخ)
            $day_of_week = $selected_date->format('w');
            
            // البحث عن الرحلات
            if ($trip_type == 'to_makkah') {
                // رحلات إلى مكة
                $sql = "SELECT t.*, 
                        ds.name as departure_station, 
                        as_table.name as arrival_station,
                        dc.name as departure_city,
                        ac.name as arrival_city
                        FROM trips t
                        JOIN stations ds ON t.departure_station_id = ds.id
                        JOIN stations as_table ON t.arrival_station_id = as_table.id
                        JOIN cities dc ON ds.city_id = dc.id
                        JOIN cities ac ON as_table.city_id = ac.id
                        WHERE t.trip_type = 'to_makkah' 
                        AND t.departure_station_id = ? 
                        AND t.is_active = 1
                        AND (t.available_days = '0' OR JSON_CONTAINS(t.available_days, ?))
                        ORDER BY t.departure_time";
            } else {
                // رحلات من مكة
                $sql = "SELECT t.*, 
                        ds.name as departure_station, 
                        as_table.name as arrival_station,
                        dc.name as departure_city,
                        ac.name as arrival_city
                        FROM trips t
                        JOIN stations ds ON t.departure_station_id = ds.id
                        JOIN stations as_table ON t.arrival_station_id = as_table.id
                        JOIN cities dc ON ds.city_id = dc.id
                        JOIN cities ac ON as_table.city_id = ac.id
                        WHERE t.trip_type = 'from_makkah' 
                        AND t.arrival_station_id = ? 
                        AND t.is_active = 1
                        AND (t.available_days = '0' OR JSON_CONTAINS(t.available_days, ?))
                        ORDER BY t.departure_time";
            }
            
            $stmt = $db->prepare($sql);
            $stmt->execute([$station_id, '"' . $day_of_week . '"']);
            $trips = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (empty($trips)) {
                echo json_encode(['success' => false, 'message' => 'لا توجد رحلات متاحة في هذا التاريخ']);
                exit;
            }
            
            echo json_encode(['success' => true, 'trips' => $trips]);
            break;
            
        case 'create_booking':
            $trip_id = intval($_POST['trip_id'] ?? 0);
            $trip_date = $_POST['trip_date'] ?? '';
            $customer_name = trim($_POST['customer_name'] ?? '');
            $customer_phone = trim($_POST['customer_phone'] ?? '');
            $passengers = intval($_POST['passengers'] ?? 0);
            
            if ($trip_id == 0 || empty($trip_date) || empty($customer_name) || empty($customer_phone) || $passengers == 0) {
                echo json_encode(['success' => false, 'message' => 'يرجى ملء جميع الحقول المطلوبة']);
                exit;
            }
            
            // التحقق من وجود الرحلة
            $stmt = $db->prepare("SELECT * FROM trips WHERE id = ? AND is_active = 1");
            $stmt->execute([$trip_id]);
            $trip = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$trip) {
                echo json_encode(['success' => false, 'message' => 'الرحلة غير موجودة']);
                exit;
            }
            
            // حساب المبلغ الإجمالي
            $total_price = $trip['seat_price'] * $passengers;
            
            // إنشاء رقم حجز فريد
            $booking_reference = 'BUS' . date('Ymd') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
            
            // التحقق من عدم تكرار رقم الحجز
            $stmt = $db->prepare("SELECT COUNT(*) FROM bus_bookings WHERE booking_reference = ?");
            $stmt->execute([$booking_reference]);
            while ($stmt->fetchColumn() > 0) {
                $booking_reference = 'BUS' . date('Ymd') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
                $stmt->execute([$booking_reference]);
            }
            
            // إدراج الحجز
            $stmt = $db->prepare("INSERT INTO bus_bookings (trip_id, trip_date, customer_name, customer_phone, seats_requested, total_price, booking_reference) VALUES (?, ?, ?, ?, ?, ?, ?)");
            $result = $stmt->execute([$trip_id, $trip_date, $customer_name, $customer_phone, $passengers, $total_price, $booking_reference]);
            
            if ($result) {
                echo json_encode([
                    'success' => true, 
                    'message' => 'تم الحجز بنجاح',
                    'booking_reference' => $booking_reference
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء حفظ الحجز']);
            }
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => 'عملية غير مدعومة']);
            break;
    }
    
} catch (Exception $e) {
    error_log("Bus booking error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'حدث خطأ في النظام']);
}
?>
