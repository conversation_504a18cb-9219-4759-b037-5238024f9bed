<?php
echo '
<section class="footer-style1 pt60 pb-0">
   <div class="container">
      <div class="row bb-white-light pb5 mb5">
         <div class="col-sm-5">
         <div class="footer-widget text-center text-sm-start">
            <a class="footer-logo" href="'.$Site_URL.'"><img src="'.$Site_URL.'/'.$Logo_Light.'" alt="'.$Site_Name.'"></a>
         </div>
         </div>
         <div class="col-sm-7">
         <div class="social-widget text-center text-sm-end">
            <div class="social-style1">
               <a class="text-white me-2 fw600 fz15" href="">'.tr('FOOTER_01').'</a>
               <a href="'.GetTableSet('Facebook').'" target="_blank"><i class="fab fa-facebook-f list-inline-item"></i></a>
               <a href="'.GetTableSet('Twitter').'" target="_blank"><i class="fab fa-twitter list-inline-item"></i></a>
               <a href="'.GetTableSet('Instagram').'" target="_blank"><i class="fab fa-instagram list-inline-item"></i></a>
               <a href="'.GetTableSet('Pinterest').'" target="_blank"><i class="fab fa-pinterest list-inline-item"></i></a>
               <a href="'.GetTableSet('Youtube').'" target="_blank"><i class="fab fa-youtube list-inline-item"></i></a>
            </div>
         </div>
         </div>
      </div>
      <div class="row">
         <div class="col-md-4">
            <div class="footer-widget mb-4 mb-lg-5">
               <h6 class="text-white mb5">'.tr('NAV_03').'</h6>
               <p>'.GetTableSet('FooterDesc').'</p> 
            </div>
         </div>
         <div class="col-md-8">
            <div class="row">
          
               <div class="col-md-5">
                  <div class="footer-widget link-style1 mb-4 mb-lg-5">
                     <h6 class="text-white mb5">'.tr('FOOTER_03').'</h6>
                     <div class="link-list row">
                     ';
                     $z = 0;
                     for ($i=0; $i < count($AllCities) ; $i++) { 
                        if($AllCities[$i]['parent'] == 0){
                           $z++; 
                           echo '<div class="col-6"><a href="'.$Site_URL.'/'.$AllCities[$i]['link'].'">'.$AllCities[$i]['name'].'</a></div>';
                           if($z >= 8){break;}
                        }
                     }
                     echo ' 
                     </div>
                  </div>
               </div>
               <div class="col-md-3">
                  <div class="footer-widget link-style1 mb-4 mb-lg-5">
                     <h6 class="text-white mb5">'.tr('FOOTER_04').'</h6>
                     <div class="link-list">
                        <a href="'.$Site_URL.'/hotel">الفنادق</a>
                        <a href="'.$Site_URL.'/privacy-policy">'.tr('NAV_04').'</a>
                        <a href="'.$Site_URL.'/terms-and-conditions">'.tr('NAV_05').'</a>
                        <a href="'.$Site_URL.'/contact-us">'.tr('NAV_06').'</a>
                     </div>
                  </div>
               </div>
            </div>
         </div>
 
      </div>
   </div>
   <div class="container white-bdrt1 py-4">
      <div class="row">
         <div class="col-sm-12">
           
         </div> 
      </div>
   </div>
   </section>
   <a class="scrollToHome" href="javascript:void(0)"><i class="fas fa-angle-up"></i></a>
</div>
</div>





';
include('scripts.php');

echo '

<script async src="https://d2mpatx37cqexb.cloudfront.net/delightchat-whatsapp-widget/embeds/embed.min.js"></script>
<script>
  var wa_btnSetting = {
    "btnColor":"#16BE45",
    "ctaText":"تواصل معنا",
    "cornerRadius":40,
    "marginBottom":20,
    "marginLeft":20,
    "marginRight":20,
    "btnPosition":"right",
    "whatsAppNumber":"966581487078",
    "welcomeMessage":"Hello",
    "zIndex":999999,
    "btnColorScheme":"light"
  };
  window.onload = () => {
    _waEmbed(wa_btnSetting);
  };
</script>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

</body>
</html>
';


