<?php
if (!function_exists('getTitle')) {
function getTitle()
{
  global $Title_page, $Site_Name , $Site_Title;
  if (isset($Title_page) && $Title_page != "") {
    $inputstring = trim($Title_page);
    $inputstring = $inputstring . ' | ' . $Site_Name;
  } else {
    $inputstring = $Site_Title;
  }
  //$inputstring = $inputstring . ' | ' . $Site_Name;
  $pieces = explode(" ", $inputstring);
  $first_part = implode(" ", array_splice($pieces, 0, 100));

  return trim($first_part);
}
//------------------------------------------------
function getDescr()
{
  global $Page_Description;
  global $Description;
  if (isset($Page_Description)) {
    $inputstring = trim($Page_Description);
  } else {
    $inputstring = trim($Description);
  }

  $pieces = explode(" ", $inputstring);
  $first_part = implode(" ", array_splice($pieces, 0, 25));

  return trim($first_part);
}
//------------------------------------------------
function getKeyWords()
{
  global $Page_KeyWords;
  global $Keywords;
  if (isset($Page_KeyWords)) {
    return  trim($Page_KeyWords);
  } else {
    return trim($Keywords);
  }
}
//------------------------------------------------
function getKImages()
{
  global $Page_images;
  global $Site_URL;
  if (isset($Page_images)) {
    return $Page_images;
  } else {
    return  $Site_URL . '/' . GetTableSet('Logo');
  }
}
//------------------------------------------------
function GetWords($string , $count_words){
  $pieces = explode(" ", $string);
  $first_part = implode(" ", array_splice($pieces, 0, $count_words));
  return trim($first_part);
}
//------------------------------------------------
$AllSettings = getAllFrom('*', 'settings', '', 'ORDER BY id DESC');
function GetTableSet($name)
{
  global $AllSettings;
  $res = '';
  for ($i = 0; $i <= count($AllSettings) - 1; $i++) {
    if ($AllSettings[$i]['name'] == $name) {
      $res = $AllSettings[$i]['setis'];
      break;
    }
  }
  return $res;
}
//------------------------------------------------  
function sec_to_time($seconds)
{
  $hours = floor($seconds / 3600);
  $minutes = floor($seconds % 3600 / 60);
  $seconds = $seconds % 60;

  return sprintf("%d:%02d:%02d", $hours, $minutes, $seconds);
}
//-------------------------------------------
function sec_to_times($seconds)
{
  $hours = floor($seconds / 3600);
  $minutes = floor($seconds % 3600 / 60);
  $seconds = $seconds % 60;

  return sprintf("%02d:%02d",  $minutes, $seconds);
}
//-------------------------------------------
function GetUserName($id)
{
  $user =  getAllFrom('*', 'users', 'WHERE id = "' . $id . '" ', '');
  if (count($user) > 0) {
    return $user[0]['fullname'];
  } else {
    return 'غير معروف';
  }
}
//-------------------------------------------
function get_client_ip()
{
  $ipaddress = '';
  if (isset($_SERVER['HTTP_CLIENT_IP']))
    $ipaddress = $_SERVER['HTTP_CLIENT_IP'];
  else if (isset($_SERVER['HTTP_X_FORWARDED_FOR']))
    $ipaddress = $_SERVER['HTTP_X_FORWARDED_FOR'];
  else if (isset($_SERVER['HTTP_X_FORWARDED']))
    $ipaddress = $_SERVER['HTTP_X_FORWARDED'];
  else if (isset($_SERVER['HTTP_FORWARDED_FOR']))
    $ipaddress = $_SERVER['HTTP_FORWARDED_FOR'];
  else if (isset($_SERVER['HTTP_FORWARDED']))
    $ipaddress = $_SERVER['HTTP_FORWARDED'];
  else if (isset($_SERVER['REMOTE_ADDR']))
    $ipaddress = $_SERVER['REMOTE_ADDR'];
  else
    $ipaddress = 'UNKNOWN';
  return $ipaddress;
}
//-----------------------------------------------------
function Show_Alert($cls, $mes)
{
  return '<div class="index_main">
          <div class="alert alert-' . $cls . ' role="alert">' . $mes . '</div></div>';
}
//-----------------------------------------------------
function generate_key($length = 50, $letters = true, $numbers = true, $case = 'i')
{
  $chars = array();
  if ($numbers) {
    $chars = array_merge($chars, range(48, 57));
  }
  if ($letters or !$numbers) {
    $chars = array_merge($chars, range(65, 90), range(97, 122));
  }

  for ($string = ''; strlen($string) < $length; $string .= chr($chars[array_rand($chars)]));
  switch ($case) {
    case 'i':
    default:
      return $string;
    case 'u':
      return strtoupper($string);
    case 'l':
      return strtolower($string);
  }
}
//-----------------------------------------------------
function Generate_Hash()
{
  $hash = generate_key(10, false);
  $ch = getAllFrom('*', 'orders', 'WHERE orderid = "' . $hash . '" ', '');
  if (count($ch) > 0) {
    Generate_Hash();
  } else {
    return $hash;
  }
}
//-----------------------------------------------------
function Share_Box()
{
  global $actual_link;
  echo '
    <div class="share_box">
        <p>شارك التقرير</p>
        <a href="http://www.facebook.com/sharer/sharer.php?u=' . $actual_link . '" class="ico ico-fb-share" title="فيس بوك" target="_blank">فيس بوك</a>
        <a href="https://twitter.com/share?url=' . $actual_link . '" class="ico ico-tw-share" title="تويتر" target="_blank">تويتر</a>
        <a href="https://plus.google.com/share?url=' . $actual_link . '" class="ico ico-gp-share" title="جوجل بلس" target="_blank">جوجل بلس</a>
      </div>
  ';
}
//-----------------------------------------------------
function MakeOrderNum($orderid){
  $start = 1000;
  $oid = $orderid + $start;
  $len = 8;
  $b = $len - strlen($oid);
  $num = '';
  for ($i=1; $i <= $b ; $i++) { 
      $num .= '0';
  }
  $num .= $oid;
  return $num;
}
//-----------------------------------------------------
function GetOrderStatus($st)
{ 
  if($st == 1){
    return 'جاري التوصيل';
  }elseif($st == 2){
    return 'تم التسليم';
  }elseif($st == 3){
    return 'تم الإلغاء';
  }else{  
    return 'جاري التجهيز';
  }
   
}
//-----------------------------------------------------

function checkItem($select, $from, $value)
{
  include('config.php');
  $ststement = $db->prepare("SELECT $select FROM $from WHERE $select = ? ");
  $ststement->execute(array($value));
  $count = $ststement->rowCount();
  return $count;
}
//------------------------------------------------
function getAllFrom($select, $table, $where = NULL, $and = NULL)
{
  include('config.php');
  $ststement = $db->prepare("SELECT $select FROM $table $where $and");
  $ststement->execute();
  $all = $ststement->fetchAll();
  return $all;
}
function UpdateTable($table, $colum, $value, $where = NULL)
{
  include('config.php');
  $stmt = $db->prepare("UPDATE " . $table . " SET " . $colum . " =  " . $value . "  " . $where . "  ");
  $stmt->execute();
}

function DeleteColum($table, $where = NULL)
{
  include('config.php');
  $stmt = $db->prepare("DELETE FROM " . $table . "  " . $where . " ");
  $stmt->execute();
}

function Update($name, $val)
{
  include('config.php');
  $AllSettings = getAllFrom('*', 'settings', 'WHERE name = "' . $name . '" ', 'ORDER BY id DESC');
  if (count($AllSettings) > 0) {
    $stmt = $db->prepare("UPDATE  settings SET  setis = :edt1  WHERE  name = :edt5 ");
    $stmt->execute(array('edt1' => $val, 'edt5' => $name));
  }
}
//-----------------------------------------------------------------------

function redirect_home($URl = null, $SEconds = 0)
{

  if ($URl === null) {
    $URl = 'index.php';
  } elseif (isset($_SERVER['HTTP_REFERER']) && $_SERVER['HTTP_REFERER'] !== '') {

    $URl = $_SERVER['HTTP_REFERER'];
  } elseif ($URl == 'error') {
    $URl = 'error.php';
  } else {

    $URl = 'index.php';
  }
  header("refresh:$SEconds;url=$URl");
  exit();
}
//------------------------------------------------------------
function randomGen($min, $max, $quantity)
{
  $numbers = range($min, $max);
  shuffle($numbers);
  return array_slice($numbers, 0, $quantity);
}
//------------------------------------------------------------
function makeLinks($s)
{
  return preg_replace('@(https?://([-\w\.]+[-\w])+(:\d+)?(/([\w/_\.#-]*(\?\S+)?[^\.\s])?)?)@', '<a style="color:#132ba9" href="$1" target="_blank">$1</a>', $s);
}
//------------------------------------------------------------
function dateDiff($time1, $time2 = '', $precision = 6)
{
  // If not numeric then convert texts to unix timestamps
  if (!is_int($time1)) {
    $time1 = strtotime($time1);
  }
  if (!is_int($time2)) {
    $time2 = strtotime($time2);
  }
  if ($time2 == '') {
    $time2 = time();
  }

  // If time1 is bigger than time2
  // Then swap time1 and time2
  if ($time1 > $time2) {
    $ttime = $time1;
    $time1 = $time2;
    $time2 = $ttime;
  }

  // Set up intervals and diffs arrays


  $intervals = array('Year', 'Month', 'Day', 'Hour', 'Minute', 'Second');
  $diffs = array();

  // Loop thru all intervals
  foreach ($intervals as $interval) {
    // Create temp time from time1 and interval
    $ttime = strtotime('+1 ' . $interval, $time1);
    // Set initial values
    $add = 1;
    $looped = 0;
    // Loop until temp time is smaller than time2
    while ($time2 >= $ttime) {
      // Create new temp time from time1 and interval
      $add++;
      $ttime = strtotime("+" . $add . " " . $interval, $time1);
      $looped++;
    }

    $time1 = strtotime("+" . $looped . " " . $interval, $time1);
    $diffs[$interval] = $looped;
  }

  $count = 0;
  $times = array();
  // Loop thru all diffs
  foreach ($diffs as $interval => $value) {
    // Break if we have needed precission
    if ($count >= $precision) {
      break;
    }
    // Add value and interval 
    // if value is bigger than 0
    if ($value > 0) {
      // Add s if value is not 1
      if ($value != 1) {
        $interval .= "s";
      }
      // Add value and interval to times array
      $times[] = $value . " " . $interval;
      $count++;
    }
  }

  // Return string with times
  return implode(" - ", $times);
}
//-----------------------------------------------------------------------
function GetMonthFromDate($timestamp){
  $m = date('m', $timestamp);
  if ($m == 1) {
    $m_s = 'يناير';
  } elseif ($m == 2) {
    $m_s = 'فبراير';
  } elseif ($m == 3) {
    $m_s = 'مارس';
  } elseif ($m == 4) {
    $m_s = 'أبريل';
  } elseif ($m == 5) {
    $m_s = 'مايو';
  } elseif ($m == 6) {
    $m_s = 'يونيو';
  } elseif ($m == 7) {
    $m_s = 'يوليو';
  } elseif ($m == 8) {
    $m_s = 'أغسطس';
  } elseif ($m == 9) {
    $m_s = 'سبتمبر';
  } elseif ($m == 10) {
    $m_s = 'أكتوبر';
  } elseif ($m == 11) {
    $m_s = 'نوفمبر';
  } elseif ($m == 12) {
    $m_s = 'ديسمبر';
  } else {
    $m_s = 'غير معروف';
  }
  return $m_s;
}
//-----------------------------------------------------------------------
function Get_Date_String($date, $all = true , $day = true)
{
  $str_to_time = $date;
  $m = date('m', $str_to_time);
  $d = date('d', $str_to_time);
  $y = date('Y', $str_to_time);
  $d_s = date('l', $str_to_time);
  if ($m == 1) {
    $m_s = 'يناير';
  } elseif ($m == 2) {
    $m_s = 'فبراير';
  } elseif ($m == 3) {
    $m_s = 'مارس';
  } elseif ($m == 4) {
    $m_s = 'أبريل';
  } elseif ($m == 5) {
    $m_s = 'مايو';
  } elseif ($m == 6) {
    $m_s = 'يونيو';
  } elseif ($m == 7) {
    $m_s = 'يوليو';
  } elseif ($m == 8) {
    $m_s = 'أغسطس';
  } elseif ($m == 9) {
    $m_s = 'سبتمبر';
  } elseif ($m == 10) {
    $m_s = 'أكتوبر';
  } elseif ($m == 11) {
    $m_s = 'نوفمبر';
  } elseif ($m == 12) {
    $m_s = 'ديسمبر';
  } else {
    $m_s = 'غير معروف';
  }

  if ($d_s == 'Saturday') {
    $d_s = 'السبت';
  } elseif ($d_s == 'Sunday') {
    $d_s = 'الأحد';
  } elseif ($d_s == 'Monday') {
    $d_s = 'الإثنين';
  } elseif ($d_s == 'Tuesday') {
    $d_s = 'الثلاثاء';
  } elseif ($d_s == 'Wednesday') {
    $d_s = 'الأربعاء';
  } elseif ($d_s == 'Thursday') {
    $d_s = 'الخميس';
  } elseif ($d_s == 'Friday') {
    $d_s = 'الجمعه';
  } else {
    $d_s = 'غير معروف';
  }

  $apm = date('a', $str_to_time);
  if ($apm == 'am') {
    $apm = 'صباحاً';
  } elseif ($apm == 'pm') {
    $apm = 'مساءً';
  } else {
    $apm = 'غير معروف';
  }

  $tm = date('h:i', $str_to_time);
  if(!$day){
    $d_s = '';
  }
  if ($all) {
    return $d_s . ' ' . $d . ' ' . $m_s . ' ' . $y . ' <span class="hm">' . $tm . ' ' . $apm . ' </span>';
  } else {
    return  $d . ' ' . $m_s . ' ' . $y;
  }
}
//-------------------------------------------------------------
function getExtensions($str)
{
  $i = strrpos($str, ".");
  if (!$i) {
    return "";
  }
  $l = strlen($str) - $i;
  $ext = substr($str, $i + 1, $l);
  return $ext;
}
//-------------------------------------------------------------
function Image_Create($img)
{
  try{
  $ext = getExtensions($img);
  if ($ext == 'png') {
    return imagecreatefrompng($img);
  } elseif ($ext == 'jpeg' || $ext == 'jpg') {
    return imagecreatefromjpeg($img);
  } elseif ($ext == 'gif') {
    return imagecreatefromgif($img);
  } else {
    return false;
  }
} catch (Exception $e) { }
return false;

}
//-------------------------------------------------------------
function Make_watermark($img1, $img2, $out, $pos = "")
{
  try{
  $stamp = Image_Create($img1);
  $im    = Image_Create($img2);
  $dest_sx = imagesx($stamp);
  $dest_sy = imagesy($stamp);
  $src_sx = imagesx($im);
  $src_sy = imagesy($im);
  $marge_right = abs($src_sx - $dest_sx);
  $marge_bottom = abs($src_sy - $dest_sy);
  if ($pos != "") {
    if ($pos == "right") {
      imagecopy($im, $stamp, $marge_right - 50, $marge_bottom - 140, 0, 0, imagesx($stamp), imagesy($stamp));
    }
    if ($pos == "left") {
      imagecopy($im, $stamp,  50, $marge_bottom - 140, 0, 0, imagesx($stamp), imagesy($stamp));
    }
  } else {
    imagecopy($im, $stamp, $marge_right, $marge_bottom, 0, 0, imagesx($stamp), imagesy($stamp));
  }
  //imagecopy($im, $stamp,  ($src_sx-$dest_sx)/2 , ($src_sy-$dest_sy)/2  , 0 , 0 , imagesx($stamp), imagesy($stamp));
  imagepng($im, $out);
  return $out;
  imagedestroy($im);
  } catch (Exception $e) { }
  return '';
}
//-------------------------------------------------------------
//-------------------------------------------------------------
function compress($source, $destination, $quality)
{

  $info = getimagesize($source);

  if ($info['mime'] == 'image/jpeg')
    $image = imagecreatefromjpeg($source);

  elseif ($info['mime'] == 'image/gif')
    $image = imagecreatefromgif($source);

  elseif ($info['mime'] == 'image/png')
    $image = imagecreatefrompng($source);

  imagejpeg($image, $destination, $quality);

  return $destination;
}

//-------------------------------------------------------------
function strip_fbclid($url)
{
  $patterns = array(
    '/(\?|&)fbclid=[^&]*$/' => '',
    '/\?fbclid=[^&]*&/' => '?',
    '/&fbclid=[^&]*&/' => '&'
  );

  $search = array_keys($patterns);
  $replace = array_values($patterns);

  return preg_replace($search, $replace, $url);
}
//-------------------------------------------------------------------
function get_string_between($string, $start, $end)
{
  $string = ' ' . $string;
  $ini = strpos($string, $start);
  if ($ini == 0) return '';
  $ini += strlen($start);
  $len = strpos($string, $end, $ini) - $ini;
  return substr($string, $ini, $len);
}
//-------------------------------------------------------------------
function str_replace_first($from, $to, $content)
{
  $from = '/' . preg_quote($from, '/') . '/';

  return preg_replace($from, $to, $content, 1);
}
//--------------------------------------------------------------------

function Get_ImagesToFolder($dir)
{
  $ImagesArray = [];
  $file_display = ['jpg', 'jpeg', 'png', 'gif', 'webp' , 'mp4'];

  if (file_exists($dir) == false) {
    return ["Directory \'', $dir, '\' not found!"];
  } else {
    $dir_contents = scandir($dir);
    foreach ($dir_contents as $file) {
      $file_type = pathinfo($file, PATHINFO_EXTENSION);
      if (in_array($file_type, $file_display) == true) {
        $ImagesArray[] = $file;
      }
    }
    return $ImagesArray;
  }
}
//--------------------------------------------------------------------
function validateDate($date, $format = 'Y-m-d')
{
  $d = DateTime::createFromFormat($format, $date);
  return $d && $d->format($format) === $date;
}
//--------------------------------------------------------------------
function get_html($url)
{
  $ch = curl_init();
  curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
  curl_setopt($ch, CURLOPT_URL, $url);
  curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
  curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
  curl_setopt($ch, CURLOPT_FAILONERROR, 0);
  $data = curl_exec($ch);
  curl_close($ch);
  return $data;
}
//-----------------------------------------------------------------------    
function get_json($url, $cookie = '')
{
  $header = array();
  $header[] = 'Accept: application/json';
  $header[] = 'Cache-Control: max-age=0';
  $header[] = 'Connection: keep-alive';
  $header[] = 'Keep-Alive: 300';
  $header[] = 'Accept-Charset: ISO-8859-1,utf-8;q=0.7,*;q=0.7';
  $header[] = 'Accept-Language: en-us,en;q=0.5';
  $header[] = 'Pragma: ';

  $ch = curl_init();
  curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36');
  curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
  curl_setopt($ch, CURLOPT_URL, $url);
  curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
  if ($cookie != "") {
    curl_setopt($ch, CURLOPT_HTTPHEADER, array("Cookie: " . $cookie));
  }
  curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
  curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
  curl_setopt($ch, CURLOPT_FAILONERROR, 0);
  $data = curl_exec($ch);
  curl_close($ch);
  return json_decode($data);
}
//-----------------------------------------------------------------------  
function Send_Post_Request($url, $data, $cookie = '')
{
  $ch = curl_init();
  curl_setopt($ch, CURLOPT_URL, $url);
  if ($cookie != "") {
    curl_setopt($ch, CURLOPT_HTTPHEADER, array("Cookie: " . $cookie));
  }
  curl_setopt($ch, CURLOPT_POST, 1);
  curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
  curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
  $return = curl_exec($ch);
  curl_close($ch);
  return json_decode($return);
}
//--------------------------------------------------------------------
function dateDifference($date_1, $date_2)
{
  $seconds = $date_1 - $date_2;
  $days = floor($seconds / 86400);
  $hrs = floor($seconds / 3600);
  $mins = intval(($seconds / 60) % 60);
  $sec = intval($seconds % 60);

  if ($days > 0) {
    //echo $days;exit;
    $hrs = str_pad($hrs, 2, '0', STR_PAD_LEFT);
    $hours = $hrs - ($days * 24);
    $return_days = $days . " يوم ";
    $hrs = str_pad($hours, 2, '0', STR_PAD_LEFT);
  } else {
    $return_days = "";
    $hrs = str_pad($hrs, 2, '0', STR_PAD_LEFT);
  }

  $mins = str_pad($mins, 2, '0', STR_PAD_LEFT);
  $sec = str_pad($sec, 2, '0', STR_PAD_LEFT);

  return $return_days . $hrs . ":" . $mins . ":" . $sec;
}
//--------------------------------------------------------------------
function DownloadImage($url, $name, $path)
{
  $name = str_replace(' ', '-', $name);
  $filame = basename($url);
  $ext = strtolower(getExtensions($filame));
  $ext = 'png';
  if (!file_exists($path . $name . '.' . $ext)) {
    $ch = curl_init($url);
    $dir = './' . $path;
    $file_name = $name . '.' . $ext;
    $save_file_loc = $dir . $file_name;
    $fp = fopen($save_file_loc, 'wb');
    curl_setopt($ch, CURLOPT_FILE, $fp);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_exec($ch);
    curl_close($ch);
    fclose($fp);
    $filename = $path . $file_name;

    return $filename;
    //compress_png($filename , $filename , 9);
  } else {
    return $path . $name . '.' . $ext;
  }
}
//--------------------------------------------------------------------
function resize_imagepng($file, $newfile, $nWidth, $nHeight)
{
  $im = imagecreatefrompng($file);

  $srcWidth = imagesx($im);
  $srcHeight = imagesy($im);


  $newImg = imagecreatetruecolor($nWidth, $nHeight);
  imagealphablending($newImg, false);
  imagesavealpha($newImg, true);
  $transparent = imagecolorallocatealpha($newImg, 255, 255, 255, 127);
  imagefilledrectangle($newImg, 0, 0, $nWidth, $nHeight, $transparent);
  imagecopyresampled(
    $newImg,
    $im,
    0,
    0,
    0,
    0,
    $nWidth,
    $nHeight,
    $srcWidth,
    $srcHeight
  );

  imagepng($newImg, $newfile);
  imagedestroy($newImg);
}
//--------------------------------------------------------------------
function GetDay($date)
{
  $d_s = date('l', strtotime($date));
  if ($d_s == 'Saturday') {
    $d_s = 'السبت';
  } elseif ($d_s == 'Sunday') {
    $d_s = 'الأحد';
  } elseif ($d_s == 'Monday') {
    $d_s = 'الإثنين';
  } elseif ($d_s == 'Tuesday') {
    $d_s = 'الثلاثاء';
  } elseif ($d_s == 'Wednesday') {
    $d_s = 'الأربعاء';
  } elseif ($d_s == 'Thursday') {
    $d_s = 'الخميس';
  } elseif ($d_s == 'Friday') {
    $d_s = 'الجمعه';
  } else {
    $d_s = 'غير معروف';
  }
  return $d_s;
}
//--------------------------------------------------------------------
function DownloadAndConvert64($imageurl)
{
  $base64 = "";
  try {
    $type = pathinfo($imageurl, PATHINFO_EXTENSION);
    $base64 = 'data:image/' . $type . ';base64,' . base64_encode(get_html($imageurl));
  } catch (Exception $e) {
  }
  return $base64;
}
//--------------------------------------------------------------------
function remEntities($str)
{
  if (substr_count($str, '&') && substr_count($str, ';')) {
    // Find amper
    $amp_pos = strpos($str, '&');
    //Find the ;
    $semi_pos = strpos($str, ';');
    // Only if the ; is after the &
    if ($semi_pos > $amp_pos) {
      //is a HTML entity, try to remove
      $tmp = substr($str, 0, $amp_pos);
      $tmp = $tmp . substr($str, $semi_pos + 1, strlen($str));
      $str = $tmp;
      //Has another entity in it?
      if (substr_count($str, '&') && substr_count($str, ';'))
        $str = remEntities($tmp);
    }
  }
  return $str;
}
//--------------------------------------------------------------------
function ShareToSocial($title, $posturl, $photo)
{
  global $ShareToSocialData;

  if ($ShareToSocialData['share_facebook']) {
    $token = $ShareToSocialData['face_token'];
    $pageid = $ShareToSocialData['face_pageid'];
    $page_token = '';
    try {

      $url =  'https://graph.facebook.com/v14.0/me/accounts?access_token=' . $token;
      echo $url;
      $data = get_json($url, $ShareToSocialData['face_cookie']);
      $data = $data->data;
      if (count($data) > 0) {
        for ($i = 0; $i <= count($data) - 1; $i++) {
          if ($data[$i]->id == $pageid) {
            $page_token = $data[$i]->access_token;
            break;
          }
        }
      }
      if ($page_token != "") {
        $data = array();
        $data['access_token'] = $page_token;
        $data['message'] = $title;
        $data['link'] = $posturl;
        $post_url = 'https://graph.facebook.com/' . $pageid . '/feed';
        $res = Send_Post_Request($post_url, $data, $ShareToSocialData['face_cookie']);
      }
    } catch (Exception $e) {
    }
  }

  if ($ShareToSocialData['share_telegram']) {
    try {
      $telegram_token = $ShareToSocialData['telegram_token'];
      $telegram_channel = $ShareToSocialData['telegram_channel'];

      $cfile = new CURLFile(realpath($photo), 'image/webp', time() . '.webp');
      $data = [
        'chat_id' => '@' . $telegram_channel,
        'photo' => $cfile,
        'caption' => $title . PHP_EOL . PHP_EOL . $posturl,
      ];

      $ch = curl_init("https://api.telegram.org/bot$telegram_token/sendPhoto");
      curl_setopt($ch, CURLOPT_HEADER, false);
      curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
      curl_setopt($ch, CURLOPT_POST, 1);
      curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
      curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
      $result = curl_exec($ch);
      curl_close($ch);
    } catch (Exception $e) {
    }
  }
}
}
//--------------------------------------------------------------------