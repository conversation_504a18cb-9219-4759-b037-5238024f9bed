<?php
include('config.php');

echo "<h1>اختبار نظام الباصات</h1>";

try {
    // اختبار الاتصال بقاعدة البيانات
    echo "<p>✅ الاتصال بقاعدة البيانات: نجح</p>";
    
    // اختبار إنشاء الجداول
    $tables = ['stations', 'trips', 'bus_bookings'];
    foreach ($tables as $table) {
        $stmt = $db->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<p>✅ جدول $table: موجود</p>";
        } else {
            echo "<p>❌ جدول $table: غير موجود</p>";
        }
    }
    
    // اختبار جلب المدن
    $cities_query = $db->query("SELECT * FROM cities WHERE status = 1 ORDER BY name");
    $cities = $cities_query->fetchAll(PDO::FETCH_ASSOC);
    echo "<p>✅ عدد المدن المتاحة: " . count($cities) . "</p>";
    
    // اختبار جلب المحطات
    $stations_query = $db->query("SELECT * FROM stations");
    $stations = $stations_query->fetchAll(PDO::FETCH_ASSOC);
    echo "<p>✅ عدد المحطات: " . count($stations) . "</p>";
    
    // اختبار جلب الرحلات
    $trips_query = $db->query("SELECT * FROM trips");
    $trips = $trips_query->fetchAll(PDO::FETCH_ASSOC);
    echo "<p>✅ عدد الرحلات: " . count($trips) . "</p>";
    
} catch (Exception $e) {
    echo "<p>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>
