# نظام حجز الباصات - Bus Booking System

## نظرة عامة
تم تطوير نظام حجز الباصات بنجاح وإضافته إلى موقع معتمر. النظام يوفر إمكانية حجز رحلات الباصات من وإلى مكة المكرمة بواجهة عربية سهلة الاستخدام.

## الميزات المطبقة

### 1. قاعدة البيانات
- **جدول المحطات (stations)**: يحتوي على محطات الانطلاق في المدن المختلفة
- **جدول الرحلات (trips)**: يحتوي على تفاصيل الرحلات وأوقاتها وأسعارها
- **جدول الحجوزات (bus_bookings)**: يحفظ بيانات الحجوزات مع أرقام مرجعية فريدة

### 2. واجهة المستخدم
- تصميم عربي متجاوب (RTL)
- اختيار نوع الرحلة (ذهاب إلى مكة / عودة من مكة)
- قائمة منسدلة للمدن والمحطات
- اختيار التاريخ مع التحقق من التواريخ المتاحة
- اختيار عدد المسافرين
- عرض الرحلات المتاحة مع الأسعار

### 3. نظام التواريخ الذكي
- كل رحلة لها أيام تشغيل محددة
- القيمة "0" تعني تشغيل يومي
- القيم الأخرى تحدد أيام الأسبوع (مثل ["1","4"] للاثنين والخميس)
- التحقق التلقائي من توفر الرحلات حسب التاريخ المختار

### 4. نظام الحجز
- نموذج حجز منبثق لإدخال بيانات العميل
- إنشاء رقم حجز فريد تلقائياً
- حفظ جميع بيانات الحجز في قاعدة البيانات
- حساب السعر الإجمالي تلقائياً

## الملفات المضافة/المحدثة

### ملفات جديدة:
- `bus.php` - الصفحة الرئيسية لحجز الباصات
- `bus-ajax.php` - ملف AJAX منفصل (اختياري)
- `test-bus.php` - ملف اختبار النظام
- `simple-bus.php` - نسخة مبسطة للاختبار

### ملفات محدثة:
- `index.php` - إضافة route للباصات
- `navbar.php` - إضافة رابط الباصات في القائمة
- `home.php` - إضافة رابط الباصات في الصفحة الرئيسية
- `ajax.php` - إضافة وظائف AJAX للباصات
- `header.php` - إضافة Bootstrap و Font Awesome
- `footer.php` - إضافة JavaScript Bootstrap
- `init.php` - إصلاح مشكلة إعادة تعريف الدوال

## البيانات التجريبية المضافة

### المحطات:
- محطة الرياض المركزية
- محطة شمال الرياض
- محطة المدينة المنورة
- محطة الدمام المركزية
- محطة الأحساء
- محطة جازان
- محطة القصيم
- محطة مكة المركزية

### الرحلات:
- رحلات يومية من الرياض إلى مكة (صباحية ومسائية)
- رحلات من المدينة إلى مكة
- رحلات من الدمام إلى مكة
- رحلات العودة من مكة إلى جميع المدن
- أسعار متنوعة حسب المسافة والخدمة

## كيفية الاستخدام

### للمستخدمين:
1. زيارة الصفحة الرئيسية والنقر على "🚌 حجز الباصات"
2. أو الذهاب مباشرة إلى `/bus`
3. اختيار نوع الرحلة (ذهاب/عودة)
4. اختيار المدينة والمحطة
5. تحديد تاريخ الرحلة وعدد المسافرين
6. النقر على "أعرض الرحلات"
7. اختيار الرحلة المناسبة والنقر على "احجز الآن"
8. ملء بيانات العميل وتأكيد الحجز

### للمطورين:
- جميع وظائف AJAX موجودة في `ajax.php`
- يمكن إضافة رحلات جديدة مباشرة في قاعدة البيانات
- يمكن تخصيص التصميم من خلال CSS في `bus.php`

## الروابط المهمة
- الصفحة الرئيسية: `/`
- صفحة الباصات: `/bus`
- اختبار النظام: `/test-bus.php`
- النسخة المبسطة: `/simple-bus.php`

## ملاحظات تقنية
- النظام يستخدم PDO للاتصال بقاعدة البيانات
- جميع النصوص باللغة العربية مع دعم RTL
- التصميم متجاوب ويعمل على جميع الأجهزة
- استخدام Bootstrap 5 و Font Awesome 6
- نظام AJAX للتفاعل بدون إعادة تحميل الصفحة

## التطويرات المستقبلية المقترحة
- إضافة نظام دفع إلكتروني
- إرسال رسائل SMS تأكيد الحجز
- لوحة إدارة لإدارة الرحلات والحجوزات
- تقارير مبيعات وإحصائيات
- نظام تقييم الرحلات
- إشعارات الواتساب

---
تم تطوير النظام بنجاح وهو جاهز للاستخدام! 🚌✅
