<?php
/*
===============================================
📌 نظام حجز مقاعد الباصات – المواصفات الكاملة
===============================================

📋 فكرة النظام:
نظام بسيط لحجز مقاعد الباصات، يسمح للمستخدمين بحجز رحلات:
- ذهاب إلى مكة
- عودة من مكة

⚠️ لا يوجد أنواع باصات متعددة
⚠️ لا حاجة لتتبع عدد المقاعد المتاحة لكل رحلة (فقط عدد المقاعد المطلوبة في الحجز)
✅ يجب تخزين بيانات الحجز في قاعدة البيانات

===================================================
📄 واجهة صفحة الحجز (booking-form):
===================================================
1. اختيار نوع الرحلة: ذهاب إلى مكة / عودة من مكة
2. اختيار المدينة (يتم تحميلها من قاعدة البيانات)
3. اختيار محطة الانطلاق (تظهر بناءً على المدينة المختارة)
4. اختيار تاريخ الرحلة (فقط التواريخ المتاحة حسب الرحلة)
5. إدخال عدد المقاعد المطلوبة
6. عرض الرحلات المتاحة
7. بعد اختيار الرحلة، يدخل المستخدم اسمه ورقم هاتفه
8. يتم تخزين الحجز في قاعدة البيانات

===================================================
🧠 نظام التواريخ (متعلق بجداول الرحلات):
===================================================
- كل رحلة تحتوي على **الأيام المتاحة للتشغيل**.
- يتم تحديدها داخل حقل `available_days` في جدول الرحلات.
- هذا الحقل يخزن أحد القيم التالية:

  🔹 `"0"`  ← تعني أن الرحلة تعمل **يوميًا** بدون استثناء  
  🔹 `["1", "4"]` ← تعني أن الرحلة تعمل فقط في **الاثنين والخميس**

- عند اختيار المستخدم تاريخ الرحلة:
  ➤ يتم جلب اليوم (0 إلى 6) باستخدام `date('w')`
  ➤ ثم تتم مقارنة اليوم مع حقل `available_days`
  ➤ إن كان متطابقًا أو كانت القيمة "0"، تعرض الرحلة

===================================================
🧱 الجداول المطلوبة:
===================================================

1️⃣ جدول المدن `cities`
------------------------
- `id` (رقم المدينة)
- `name` (اسم المدينة)

2️⃣ جدول المحطات `stations`
----------------------------
- `id` (رقم المحطة)
- `city_id` (مرجع المدينة)
- `name` (اسم المحطة)

3️⃣ جدول الرحلات `trips`
-------------------------
- `id` (رقم الرحلة)
- `trip_type` (to_makkah / from_makkah)
- `departure_station_id` (محطة الانطلاق)
- `arrival_station_id` (محطة الوصول)
- `departure_time` (ساعة الانطلاق)
- `expected_arrival_time` (ساعة الوصول المتوقعة)
- `seat_price` (سعر المقعد)
- `available_days` (الأيام المتاحة: "0" أو ["1", "4"])
- `is_active` (هل الرحلة مفعّلة؟)

4️⃣ جدول الحجوزات `bookings`
-----------------------------
- `id` (رقم الحجز)
- `trip_id` (رقم الرحلة)
- `trip_date` (تاريخ الرحلة)
- `customer_name` (اسم العميل)
- `customer_phone` (رقم الهاتف)
- `seats_requested` (عدد المقاعد المطلوبة)
- `created_at` (تاريخ ووقت إنشاء الحجز)

===================================================
📌 ملاحظات إضافية:
===================================================
- لا حاجة لحساب المقاعد المتبقية، يتم فقط تخزين عدد المقاعد المطلوبة.
- يمكن لاحقًا توسيع النظام بإرسال SMS، لوحة إدارة الحجوزات، أو الدفع الإلكتروني.
- حقل `available_days` يمكن استخدامه بسهولة في PHP مع JSON و `date('w')` للتحكم في ظهور التواريخ.

*/
?>
