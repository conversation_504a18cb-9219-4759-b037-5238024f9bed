<?php
include('webset.php');
include('header.php');
include('navbar.php');

// إنشاء الجداول إذا لم تكن موجودة
try {
    // التحقق من وجود قاعدة البيانات
    if (!$db) {
        throw new Exception("Database connection failed");
    }
    // جدول المحطات
    $db->exec("CREATE TABLE IF NOT EXISTS `stations` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `city_id` int(11) NOT NULL,
        `name` varchar(255) NOT NULL,
        `address` text DEFAULT NULL,
        `is_active` tinyint(1) DEFAULT 1,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `city_id` (`city_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // جدول الرحلات
    $db->exec("CREATE TABLE IF NOT EXISTS `trips` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `trip_type` enum('to_makkah','from_makkah') NOT NULL,
        `departure_station_id` int(11) NOT NULL,
        `arrival_station_id` int(11) NOT NULL,
        `departure_time` time NOT NULL,
        `expected_arrival_time` time NOT NULL,
        `seat_price` decimal(8,2) NOT NULL,
        `available_days` text NOT NULL COMMENT 'JSON array of available days or \"0\" for daily',
        `is_active` tinyint(1) DEFAULT 1,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `departure_station_id` (`departure_station_id`),
        KEY `arrival_station_id` (`arrival_station_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // جدول الحجوزات
    $db->exec("CREATE TABLE IF NOT EXISTS `bus_bookings` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `trip_id` int(11) NOT NULL,
        `trip_date` date NOT NULL,
        `customer_name` varchar(255) NOT NULL,
        `customer_phone` varchar(20) NOT NULL,
        `seats_requested` int(11) NOT NULL,
        `total_price` decimal(8,2) NOT NULL,
        `booking_reference` varchar(20) NOT NULL,
        `status` enum('pending','confirmed','cancelled') DEFAULT 'confirmed',
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `booking_reference` (`booking_reference`),
        KEY `trip_id` (`trip_id`),
        KEY `trip_date` (`trip_date`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // إدراج بيانات تجريبية للمحطات
    $stations_data = [
        ['city_id' => 1, 'name' => 'محطة الرياض المركزية', 'address' => 'شارع الملك فهد، الرياض'],
        ['city_id' => 1, 'name' => 'محطة شمال الرياض', 'address' => 'حي النرجس، الرياض'],
        ['city_id' => 3, 'name' => 'محطة المدينة المنورة', 'address' => 'شارع الأمير عبدالمحسن، المدينة المنورة'],
        ['city_id' => 4, 'name' => 'محطة الدمام المركزية', 'address' => 'شارع الملك سعود، الدمام'],
        ['city_id' => 5, 'name' => 'محطة الأحساء', 'address' => 'شارع الملك عبدالعزيز، الأحساء'],
        ['city_id' => 6, 'name' => 'محطة جازان', 'address' => 'شارع الملك فهد، جازان'],
        ['city_id' => 7, 'name' => 'محطة القصيم', 'address' => 'شارع الملك عبدالعزيز، بريدة'],
        ['city_id' => 8, 'name' => 'محطة مكة المركزية', 'address' => 'شارع إبراهيم الخليل، مكة المكرمة']
    ];

    foreach ($stations_data as $station) {
        $check = $db->prepare("SELECT COUNT(*) FROM stations WHERE city_id = ? AND name = ?");
        $check->execute([$station['city_id'], $station['name']]);
        if ($check->fetchColumn() == 0) {
            $stmt = $db->prepare("INSERT INTO stations (city_id, name, address) VALUES (?, ?, ?)");
            $stmt->execute([$station['city_id'], $station['name'], $station['address']]);
        }
    }

    // إدراج بيانات تجريبية للرحلات
    $trips_data = [
        // رحلات من الرياض إلى مكة
        ['trip_type' => 'to_makkah', 'departure_station_id' => 1, 'arrival_station_id' => 8, 'departure_time' => '06:00:00', 'expected_arrival_time' => '16:00:00', 'seat_price' => 120.00, 'available_days' => '0'],
        ['trip_type' => 'to_makkah', 'departure_station_id' => 1, 'arrival_station_id' => 8, 'departure_time' => '14:00:00', 'expected_arrival_time' => '00:00:00', 'seat_price' => 120.00, 'available_days' => '0'],
        ['trip_type' => 'to_makkah', 'departure_station_id' => 2, 'arrival_station_id' => 8, 'departure_time' => '08:00:00', 'expected_arrival_time' => '18:00:00', 'seat_price' => 150.00, 'available_days' => '["1","4"]'],

        // رحلات من المدينة إلى مكة
        ['trip_type' => 'to_makkah', 'departure_station_id' => 3, 'arrival_station_id' => 8, 'departure_time' => '10:00:00', 'expected_arrival_time' => '14:00:00', 'seat_price' => 80.00, 'available_days' => '0'],

        // رحلات من الدمام إلى مكة
        ['trip_type' => 'to_makkah', 'departure_station_id' => 4, 'arrival_station_id' => 8, 'departure_time' => '05:00:00', 'expected_arrival_time' => '18:00:00', 'seat_price' => 180.00, 'available_days' => '0'],

        // رحلات العودة من مكة
        ['trip_type' => 'from_makkah', 'departure_station_id' => 8, 'arrival_station_id' => 1, 'departure_time' => '20:00:00', 'expected_arrival_time' => '06:00:00', 'seat_price' => 120.00, 'available_days' => '0'],
        ['trip_type' => 'from_makkah', 'departure_station_id' => 8, 'arrival_station_id' => 3, 'departure_time' => '18:00:00', 'expected_arrival_time' => '22:00:00', 'seat_price' => 80.00, 'available_days' => '0'],
        ['trip_type' => 'from_makkah', 'departure_station_id' => 8, 'arrival_station_id' => 4, 'departure_time' => '19:00:00', 'expected_arrival_time' => '08:00:00', 'seat_price' => 180.00, 'available_days' => '0']
    ];

    foreach ($trips_data as $trip) {
        $check = $db->prepare("SELECT COUNT(*) FROM trips WHERE departure_station_id = ? AND arrival_station_id = ? AND departure_time = ?");
        $check->execute([$trip['departure_station_id'], $trip['arrival_station_id'], $trip['departure_time']]);
        if ($check->fetchColumn() == 0) {
            $stmt = $db->prepare("INSERT INTO trips (trip_type, departure_station_id, arrival_station_id, departure_time, expected_arrival_time, seat_price, available_days) VALUES (?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([$trip['trip_type'], $trip['departure_station_id'], $trip['arrival_station_id'], $trip['departure_time'], $trip['expected_arrival_time'], $trip['seat_price'], $trip['available_days']]);
        }
    }

} catch (Exception $e) {
    error_log("Database setup error: " . $e->getMessage());
}

// جلب جميع المدن
$cities_query = $db->query("SELECT * FROM cities WHERE status = 1 ORDER BY name");
$cities = $cities_query->fetchAll(PDO::FETCH_ASSOC);
?>

<style>
.bus-booking-section {
    background: linear-gradient(135deg, #ff8c00 0%, #ff6b00 100%);
    min-height: 100vh;
    padding: 50px 0;
    position: relative;
    overflow: hidden;
}

.bus-booking-section::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 60%;
    height: 100%;
    background: url('img/bus-1.jpeg') no-repeat center center;
    background-size: contain;
    opacity: 0.1;
    z-index: 1;
}

.booking-container {
    position: relative;
    z-index: 2;
}

.booking-form-card {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.1);
    max-width: 500px;
    margin: 0 auto;
}

.booking-title {
    color: #333;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 30px;
    text-align: center;
}

.form-group {
    margin-bottom: 25px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 600;
    font-size: 14px;
}

.form-control {
    width: 100%;
    padding: 15px;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.form-control:focus {
    outline: none;
    border-color: #ff8c00;
    background: white;
    box-shadow: 0 0 0 3px rgba(255, 140, 0, 0.1);
}

.trip-type-selector {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
}

.trip-type-option {
    flex: 1;
    position: relative;
}

.trip-type-option input[type="radio"] {
    display: none;
}

.trip-type-label {
    display: block;
    padding: 15px;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9fa;
    font-weight: 600;
}

.trip-type-option input[type="radio"]:checked + .trip-type-label {
    border-color: #ff8c00;
    background: #ff8c00;
    color: white;
}

.search-btn {
    width: 100%;
    padding: 18px;
    background: linear-gradient(135deg, #ff8c00 0%, #ff6b00 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 20px;
}

.search-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(255, 140, 0, 0.3);
}

.page-title {
    color: white;
    font-size: 48px;
    font-weight: bold;
    text-align: right;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.page-subtitle {
    color: white;
    font-size: 18px;
    text-align: right;
    margin-bottom: 40px;
    opacity: 0.9;
}

.trips-results {
    margin-top: 40px;
    display: none;
}

.trip-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.trip-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.trip-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 15px;
}

.trip-route {
    font-size: 18px;
    font-weight: bold;
    color: #333;
}

.trip-price {
    font-size: 24px;
    font-weight: bold;
    color: #ff8c00;
}

.trip-details {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    color: #666;
}

.book-trip-btn {
    width: 100%;
    padding: 12px;
    background: #ff8c00;
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.book-trip-btn:hover {
    background: #ff6b00;
}

@media (max-width: 768px) {
    .page-title {
        font-size: 32px;
        text-align: center;
    }

    .page-subtitle {
        text-align: center;
    }

    .booking-form-card {
        margin: 20px;
        padding: 25px;
    }

    .trip-type-selector {
        flex-direction: column;
        gap: 10px;
    }
}
</style>

<section class="bus-booking-section">
    <div class="container booking-container">
        <div class="row">
            <div class="col-lg-6">
                <h1 class="page-title">باصات من والى مكة يوميا</h1>
                <p class="page-subtitle">احجز رحلتك بكل سهولة وراحة مع أفضل خدمات النقل</p>

                <div class="booking-form-card">
                    <h2 class="booking-title">🚌 حجز رحلة الباص</h2>

                    <form id="busBookingForm">
                        <!-- نوع الرحلة -->
                        <div class="trip-type-selector">
                            <div class="trip-type-option">
                                <input type="radio" id="to_makkah" name="trip_type" value="to_makkah" checked>
                                <label for="to_makkah" class="trip-type-label">
                                    ذهاب إلى مكة
                                </label>
                            </div>
                            <div class="trip-type-option">
                                <input type="radio" id="from_makkah" name="trip_type" value="from_makkah">
                                <label for="from_makkah" class="trip-type-label">
                                    عودة من مكة
                                </label>
                            </div>
                        </div>

                        <!-- المدينة -->
                        <div class="form-group">
                            <label class="form-label">المدينة</label>
                            <select class="form-control" id="city_select" name="city_id" required>
                                <option value="">اختر المدينة</option>
                                <?php foreach ($cities as $city): ?>
                                    <option value="<?= $city['id'] ?>"><?= $city['name'] ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- المحطة -->
                        <div class="form-group">
                            <label class="form-label">المحطة</label>
                            <select class="form-control" id="station_select" name="station_id" required disabled>
                                <option value="">اختر المحطة</option>
                            </select>
                        </div>

                        <!-- تاريخ الرحلة -->
                        <div class="form-group">
                            <label class="form-label">تاريخ الرحلة</label>
                            <input type="date" class="form-control" id="trip_date" name="trip_date" required min="<?= date('Y-m-d') ?>">
                        </div>

                        <!-- عدد المسافرين -->
                        <div class="form-group">
                            <label class="form-label">عدد المسافرين</label>
                            <select class="form-control" name="passengers" required>
                                <option value="">اختر عدد المسافرين</option>
                                <?php for ($i = 1; $i <= 10; $i++): ?>
                                    <option value="<?= $i ?>"><?= $i ?> <?= $i == 1 ? 'مسافر' : 'مسافرين' ?></option>
                                <?php endfor; ?>
                            </select>
                        </div>

                        <button type="submit" class="search-btn">
                            <i class="fas fa-search"></i> أعرض الرحلات
                        </button>
                    </form>
                </div>
            </div>

            <div class="col-lg-6">
                <!-- نتائج البحث ستظهر هنا -->
                <div id="trips-results" class="trips-results">
                    <h3 style="color: white; margin-bottom: 25px;">الرحلات المتاحة</h3>
                    <div id="trips-container"></div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- نموذج الحجز المنبثق -->
<div class="modal fade" id="bookingModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إتمام الحجز</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="finalBookingForm">
                    <input type="hidden" id="selected_trip_id" name="trip_id">
                    <input type="hidden" id="selected_trip_date" name="trip_date">
                    <input type="hidden" id="selected_passengers" name="passengers">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">الاسم الكامل</label>
                                <input type="text" class="form-control" name="customer_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" name="customer_phone" required>
                            </div>
                        </div>
                    </div>

                    <div id="booking-summary" class="mt-4 p-3" style="background: #f8f9fa; border-radius: 10px;">
                        <!-- ملخص الحجز سيظهر هنا -->
                    </div>

                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-primary btn-lg">تأكيد الحجز</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const citySelect = document.getElementById('city_select');
    const stationSelect = document.getElementById('station_select');
    const busBookingForm = document.getElementById('busBookingForm');
    const tripsResults = document.getElementById('trips-results');
    const tripsContainer = document.getElementById('trips-container');

    // تحديث المحطات عند تغيير المدينة
    citySelect.addEventListener('change', function() {
        const cityId = this.value;
        stationSelect.innerHTML = '<option value="">اختر المحطة</option>';
        stationSelect.disabled = !cityId;

        if (cityId) {
            fetch('ajax.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=get_stations&city_id=${cityId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    data.stations.forEach(station => {
                        const option = document.createElement('option');
                        option.value = station.id;
                        option.textContent = station.name;
                        stationSelect.appendChild(option);
                    });
                    stationSelect.disabled = false;
                }
            })
            .catch(error => console.error('Error:', error));
        }
    });

    // البحث عن الرحلات
    busBookingForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        formData.append('action', 'search_trips');

        fetch('ajax.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayTrips(data.trips, formData.get('passengers'));
                tripsResults.style.display = 'block';
            } else {
                alert(data.message || 'لم يتم العثور على رحلات متاحة');
                tripsResults.style.display = 'none';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء البحث');
        });
    });

    // عرض الرحلات
    function displayTrips(trips, passengers) {
        tripsContainer.innerHTML = '';

        trips.forEach(trip => {
            const totalPrice = trip.seat_price * passengers;
            const tripCard = document.createElement('div');
            tripCard.className = 'trip-card';
            tripCard.innerHTML = `
                <div class="trip-header">
                    <div class="trip-route">${trip.departure_station} → ${trip.arrival_station}</div>
                    <div class="trip-price">${totalPrice} ريال</div>
                </div>
                <div class="trip-details">
                    <div><i class="fas fa-clock"></i> ${trip.departure_time}</div>
                    <div><i class="fas fa-map-marker-alt"></i> ${trip.expected_arrival_time}</div>
                    <div><i class="fas fa-user"></i> ${passengers} مسافر</div>
                </div>
                <button class="book-trip-btn" onclick="openBookingModal(${trip.id}, '${document.getElementById('trip_date').value}', ${passengers}, '${trip.departure_station}', '${trip.arrival_station}', '${trip.departure_time}', ${totalPrice})">
                    احجز الآن
                </button>
            `;
            tripsContainer.appendChild(tripCard);
        });
    }

    // نموذج الحجز النهائي
    document.getElementById('finalBookingForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        formData.append('action', 'create_booking');

        fetch('ajax.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`تم الحجز بنجاح! رقم الحجز: ${data.booking_reference}`);
                location.reload();
            } else {
                alert(data.message || 'حدث خطأ أثناء الحجز');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء الحجز');
        });
    });
});

// فتح نموذج الحجز
function openBookingModal(tripId, tripDate, passengers, departureStation, arrivalStation, departureTime, totalPrice) {
    document.getElementById('selected_trip_id').value = tripId;
    document.getElementById('selected_trip_date').value = tripDate;
    document.getElementById('selected_passengers').value = passengers;

    document.getElementById('booking-summary').innerHTML = `
        <h6>ملخص الحجز:</h6>
        <p><strong>المسار:</strong> ${departureStation} → ${arrivalStation}</p>
        <p><strong>التاريخ:</strong> ${tripDate}</p>
        <p><strong>وقت المغادرة:</strong> ${departureTime}</p>
        <p><strong>عدد المسافرين:</strong> ${passengers}</p>
        <p><strong>المبلغ الإجمالي:</strong> ${totalPrice} ريال</p>
    `;

    const modal = new bootstrap.Modal(document.getElementById('bookingModal'));
    modal.show();
}
</script>

<?php include('footer.php'); ?>
