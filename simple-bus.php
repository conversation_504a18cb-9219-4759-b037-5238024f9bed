<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام حجز الباصات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b00 100%);
            min-height: 100vh;
        }
        .booking-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            margin: 50px auto;
            max-width: 600px;
        }
        .form-control {
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .btn-primary {
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b00 100%);
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-weight: bold;
        }
        .trip-type-selector {
            display: flex;
            gap: 15px;
            margin-bottom: 25px;
        }
        .trip-type-option {
            flex: 1;
        }
        .trip-type-option input[type="radio"] {
            display: none;
        }
        .trip-type-label {
            display: block;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8f9fa;
            font-weight: 600;
        }
        .trip-type-option input[type="radio"]:checked + .trip-type-label {
            border-color: #ff8c00;
            background: #ff8c00;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="booking-card">
            <h2 class="text-center mb-4">🚌 حجز رحلة الباص</h2>
            
            <form id="busBookingForm">
                <!-- نوع الرحلة -->
                <div class="trip-type-selector">
                    <div class="trip-type-option">
                        <input type="radio" id="to_makkah" name="trip_type" value="to_makkah" checked>
                        <label for="to_makkah" class="trip-type-label">
                            ذهاب إلى مكة
                        </label>
                    </div>
                    <div class="trip-type-option">
                        <input type="radio" id="from_makkah" name="trip_type" value="from_makkah">
                        <label for="from_makkah" class="trip-type-label">
                            عودة من مكة
                        </label>
                    </div>
                </div>

                <!-- المدينة -->
                <div class="mb-3">
                    <label class="form-label">المدينة</label>
                    <select class="form-control" id="city_select" name="city_id" required>
                        <option value="">اختر المدينة</option>
                        <?php
                        include('config.php');
                        $cities_query = $db->query("SELECT * FROM cities WHERE status = 1 ORDER BY name");
                        $cities = $cities_query->fetchAll(PDO::FETCH_ASSOC);
                        foreach ($cities as $city): ?>
                            <option value="<?= $city['id'] ?>"><?= $city['name'] ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <!-- المحطة -->
                <div class="mb-3">
                    <label class="form-label">المحطة</label>
                    <select class="form-control" id="station_select" name="station_id" required disabled>
                        <option value="">اختر المحطة</option>
                    </select>
                </div>

                <!-- تاريخ الرحلة -->
                <div class="mb-3">
                    <label class="form-label">تاريخ الرحلة</label>
                    <input type="date" class="form-control" id="trip_date" name="trip_date" required min="<?= date('Y-m-d') ?>">
                </div>

                <!-- عدد المسافرين -->
                <div class="mb-3">
                    <label class="form-label">عدد المسافرين</label>
                    <select class="form-control" name="passengers" required>
                        <option value="">اختر عدد المسافرين</option>
                        <?php for ($i = 1; $i <= 10; $i++): ?>
                            <option value="<?= $i ?>"><?= $i ?> <?= $i == 1 ? 'مسافر' : 'مسافرين' ?></option>
                        <?php endfor; ?>
                    </select>
                </div>

                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search"></i> أعرض الرحلات
                </button>
            </form>
            
            <!-- نتائج البحث -->
            <div id="trips-results" class="mt-4" style="display: none;">
                <h4>الرحلات المتاحة</h4>
                <div id="trips-container"></div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const citySelect = document.getElementById('city_select');
            const stationSelect = document.getElementById('station_select');
            const busBookingForm = document.getElementById('busBookingForm');
            
            // تحديث المحطات عند تغيير المدينة
            citySelect.addEventListener('change', function() {
                const cityId = this.value;
                stationSelect.innerHTML = '<option value="">اختر المحطة</option>';
                stationSelect.disabled = !cityId;
                
                if (cityId) {
                    fetch('ajax.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `action=get_stations&city_id=${cityId}`
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            data.stations.forEach(station => {
                                const option = document.createElement('option');
                                option.value = station.id;
                                option.textContent = station.name;
                                stationSelect.appendChild(option);
                            });
                            stationSelect.disabled = false;
                        }
                    })
                    .catch(error => console.error('Error:', error));
                }
            });
            
            // البحث عن الرحلات
            busBookingForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const formData = new FormData(this);
                formData.append('action', 'search_trips');
                
                fetch('ajax.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayTrips(data.trips, formData.get('passengers'));
                        document.getElementById('trips-results').style.display = 'block';
                    } else {
                        alert(data.message || 'لم يتم العثور على رحلات متاحة');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء البحث');
                });
            });
            
            // عرض الرحلات
            function displayTrips(trips, passengers) {
                const container = document.getElementById('trips-container');
                container.innerHTML = '';
                
                trips.forEach(trip => {
                    const totalPrice = trip.seat_price * passengers;
                    const tripCard = document.createElement('div');
                    tripCard.className = 'card mb-3';
                    tripCard.innerHTML = `
                        <div class="card-body">
                            <h5 class="card-title">${trip.departure_station} → ${trip.arrival_station}</h5>
                            <p class="card-text">
                                <i class="fas fa-clock"></i> ${trip.departure_time} - ${trip.expected_arrival_time}<br>
                                <i class="fas fa-user"></i> ${passengers} مسافر<br>
                                <strong>السعر: ${totalPrice} ريال</strong>
                            </p>
                            <button class="btn btn-success" onclick="alert('سيتم إضافة نموذج الحجز قريباً')">
                                احجز الآن
                            </button>
                        </div>
                    `;
                    container.appendChild(tripCard);
                });
            }
        });
    </script>
</body>
</html>
