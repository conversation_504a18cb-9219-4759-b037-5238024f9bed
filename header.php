<?php
echo '<!doctype html>
<html dir="rtl" lang="ar">
    <head>
        <meta charset="utf-8">';

if (GetTableSet('LockContent') == 1) {
    echo '
    <script>
        window.onload = function () {
            function e(e) {
                return e.stopPropagation ? e.stopPropagation() : window.event && (window.event.cancelBubble = !0), e.preventDefault(), !1;
            }
            document.addEventListener("contextmenu", function (e) { e.preventDefault() }, !1);
            document.addEventListener("keydown", function (t) {
                t.ctrlKey && t.shiftKey && 73 == t.keyCode && e(t);
                t.ctrlKey && t.shiftKey && 74 == t.keyCode && e(t);
                83 == t.keyCode && (navigator.platform.match("Mac") ? t.metaKey : t.ctrlKey) && e(t);
                t.ctrl<PERSON>ey && 85 == t.keyCode && e(t);
                123 == event.keyCode && e(t);
            }, !1);
        };
    </script>';
}

echo '
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <title>' . getTitle() . '</title>
        <meta name="description" content="' . getDescr() . '" />
        <meta name="keywords" content="' . getKeyWords() . '">
        <meta name="viewport" content="width=device-width, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
        <meta name="author" content="moatamer" />
        <meta property="og:url" content="' . strip_fbclid($actual_link) . '" />
        <meta property="og:type" content="product" />
        <meta property="og:title" content="' . getTitle() . '">
        <meta property="og:image" content="' . getKImages() . '" />
        <meta property="og:image:alt" content="' . getTitle() . '" />
        <meta property="og:image:type" content="image/PNG" />
        <meta property="og:image:width" content="500" />
        <meta property="og:image:height" content="500" />
        <meta property="og:description" content="' . getDescr() . '">
        <meta property="og:keywords" content="' . getKeyWords() . '">
        <meta name="google-site-verification" content="' . GetTableSet('google-site-verification') . '" />
        <link id="favicon1" rel="icon" href="' . $Site_URL . '/img/flv.png">
        <link id="favicon2" rel="shortcut icon" href="' . $Site_URL . '/img/flv.png">
        <meta name="theme-color" content="' . GetTableSet('theme_color') . '" />
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.css" integrity="sha512-5A8nwdMOWrSz20fDsjczgUidUBR8liPYU+WymTZP1lmY9G6Oc7HlZv156XqnsgNUzTyMefFTcsFH/tnJE/+xBg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        ';

include('style.php');

echo '
    </head>
    <body>
';

?>

<?php
